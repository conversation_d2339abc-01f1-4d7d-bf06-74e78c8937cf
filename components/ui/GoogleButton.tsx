import FontAwesome from "@expo/vector-icons/FontAwesome";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { router } from "expo-router";
import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

import { useAuth } from "@/context/AuthContext";
import { useGoogleLogin } from "@/lib/api/hooks";

interface GoogleButtonProps {
  signup?: boolean;
}

export function GoogleButton({ signup = false }: GoogleButtonProps) {
  const { signIn } = useAuth();
  const googleLoginMutation = useGoogleLogin();

  const handleGoogleSignIn = async () => {
    console.log("GoogleButton pressed!");
    GoogleSignin.configure({
      webClientId:
        "119719605721-uta6gohluc84nrkh75el8jbs642bdcr6.apps.googleusercontent.com",
      iosClientId:
        "119719605721-uta6gohluc84nrkh75el8jbs642bdcr6.apps.googleusercontent.com",
      offlineAccess: true,
    });

    try {
      console.log("Starting Google Sign-In...");
      const user = await GoogleSignin.signIn({});
      console.log("Google Sign-In successful:", user);

      // Extract the idToken from the Google sign-in response
      const idToken = user.data?.idToken;
      if (!idToken) {
        console.error("No idToken received from Google");
        return;
      }

      // Get platform-specific client ID
      const clientId =
        Platform.OS === "ios"
          ? process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID!
          : process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID!;

      // Call the Google login API using the mutation
      const response = await googleLoginMutation.mutateAsync({
        idToken: idToken,
        clientId: clientId,
      });

      console.log("Google login response:", response);

      // Sign in with the received access token
      await signIn(response.accessToken, response.expiresInSeconds);

      // Log whether this was a new user registration
      if (response.isNewUser) {
        console.log("New user registered via Google login");
      } else {
        console.log("Existing user signed in via Google");
      }

      // Navigate to home page
      router.replace("/(app)/home");
    } catch (e) {
      console.error("Google sign in failed with error:", e);
      console.error("Error details:", JSON.stringify(e, null, 2));
      // You might want to show an alert to the user here
    }
  };

  return (
    <TouchableOpacity style={styles.googleButton} onPress={handleGoogleSignIn}>
      <View style={styles.googleButtonContent}>
        <FontAwesome name="google" size={20} color="#000" />
        <Text style={styles.googleButtonText}>
          {signup ? "Sign up with Google" : "Sign in with Google"}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  googleButton: {
    backgroundColor: "#FFFFFF",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 24,
  },
  googleButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  googleButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
});
